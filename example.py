import numpy as np
from rlgym_sim.utils.gamestates import GameState
from lookup_act import LookupAction
from rlgym_ppo.util import MetricsLogger
from state_setters import ProbabilisticStateSetter, DribblingStateSetter
from customreward import KickoffProximityReward, ZeroSumReward, SwiftGroundDribbleReward, AirTouchReward, CradleFlickReward, LemTouchBallReward, RetreatReward, DistanceReward, AerialDistanceReward, InAirReward, CradleReward, GroundedReward, GroundDribbleReward, JumpTouchReward, WallDashHelperReward, WallDashReward, RefinedDemoReward, SpeedFlipKickoffReward, OwnGoalPenaltyReward, BouncyAirDribbleReward, BouncyAirDribbleHelperReward
from rlgym_sim.utils.reward_functions import CombinedReward
from rlgym_sim.utils.reward_functions.common_rewards import (
    VelocityBallToGoalReward, VelocityPlayerToBallReward, EventReward, FaceBallReward, SaveBoostReward, TouchBallReward, LiuDistanceBallToGoalReward, 
    AlignBallGoal
)
from rlgym_sim.utils.terminal_conditions.common_conditions import NoTouchTimeoutCondition, GoalScoredCondition
from rlgym_sim.utils.state_setters import RandomState, DefaultState
from obs import AdvancedObsPadder
# Add custom LogCombinedReward
from customreward import LogCombinedReward, GoodVelocityPlayerToBallReward, FlatSpeedReward

g_combined_reward = None  # type: LogCombinedReward

class ExampleLogger(MetricsLogger):
    def __init__(self):
        super().__init__()
        self.reward_history = []
        self.event_counts = {}
        
    def _collect_metrics(self, game_state: GameState) -> list:
        metrics = [game_state.players[0].car_data.linear_velocity,
                  game_state.players[0].car_data.rotation_mtx(),
                  game_state.orange_score]
        
        if g_combined_reward and g_combined_reward.prev_rewards:
            self.reward_history.append(g_combined_reward.prev_rewards)
            metrics.append(g_combined_reward.prev_rewards)
            
            # Track counts for any non-zero rewards
            for i, reward in enumerate(g_combined_reward.prev_rewards):
                if reward != 0:
                    name = g_combined_reward.reward_functions[i].__class__.__name__
                    if name not in self.event_counts:
                        self.event_counts[name] = {"count": 0, "total_reward": 0}
                    self.event_counts[name]["count"] += 1
                    self.event_counts[name]["total_reward"] += reward * g_combined_reward.reward_weights[i]
                    
        return metrics

    def _report_metrics(self, collected_metrics, wandb_run, cumulative_timesteps):
        print(f"\nIteration Report (Timestep {cumulative_timesteps})")
        print("--------------------------------")
        
        # Calculate velocity averages
        avg_linvel = np.zeros(3)
        for metric_array in collected_metrics:
            avg_linvel += metric_array[0]
        avg_linvel /= len(collected_metrics)
        
        # Basic stats for wandb
        report = {
            "x_vel": avg_linvel[0],
            "y_vel": avg_linvel[1],
            "z_vel": avg_linvel[2],
            "Cumulative Timesteps": cumulative_timesteps
        }

        # Show event statistics
        if self.event_counts:
            print("\nEvent Statistics:")
            print("----------------")
            total_reward = 0
            
            # Sort by total reward contribution
            sorted_events = sorted(self.event_counts.items(), 
                                 key=lambda x: abs(x[1]["total_reward"]), 
                                 reverse=True)
            
            for name, stats in sorted_events:
                count = stats["count"]
                total = stats["total_reward"]
                avg = total / count if count > 0 else 0
                
                print(f"{name}:")
                print(f"  Times triggered: {count}")
                print(f"  Total reward: {total:.2f}")
                print(f"  Average per trigger: {avg:.2f}")
                
                total_reward += total
                
                # Log to wandb
                report[f"{name} Count"] = count
                report[f"{name} Total Reward"] = total
                
            print(f"\nTotal Reward: {total_reward:.2f}")
            report["Total Reward"] = total_reward
            
        wandb_run.log(report)
        
        # Clear history for next iteration
        self.reward_history.clear()
        self.event_counts.clear()

def build_rocketsim_env():
    import rlgym_sim
    from customreward import LogCombinedReward, SaveBoostReward, BoostAcquisitions


    spawn_opponents = True
    team_size = 1
    game_tick_rate = 120
    tick_skip = 8
    timeout_seconds = 10
    timeout_ticks = int(round(timeout_seconds * game_tick_rate / tick_skip))

    action_parser = LookupAction()    
    terminal_conditions = [NoTouchTimeoutCondition(timeout_ticks), GoalScoredCondition()]    
    reward_fn = LogCombinedReward.from_zipped(
        # Core Game Events (scoring, saves, shots)
        (EventReward(goal=75, concede=-50), 7.5), 
        (EventReward(shot=5, save=7.5), 4.0),
        (OwnGoalPenaltyReward(), 100),
        
        # Ball Control & Movement
        (GoodVelocityPlayerToBallReward(), 7),    # Smart ball approach
        (VelocityBallToGoalReward(), 10),         # Shooting power and accuracy
        (FaceBallReward(), 1),                    # Basic ball awareness
        (CradleReward(), 1),
        (CradleFlickReward(), 5),
        
        # Advanced Mechanics
        (WallDashReward(), 50),                   # Main wall play reward
        (WallDashHelperReward(), 15),              # Support for wall mechanics 1-3 ratio with helper and main reward
        (SpeedFlipKickoffReward(), 25),           # Heavily incentivize speedflip kickoffs
        (InAirReward(), 0.1),                    # Very small aerial incentive
        (BouncyAirDribbleReward(), 10),
        (BouncyAirDribbleHelperReward(), 4.5),
        
        # Game Awareness & Utility
        (EventReward(boost_pickup=0.3, touch=5), 2.5),  # Boost and ball interaction
        (RefinedDemoReward(), 10),                     # Tactical demolitions
        
        # Boost Management
        (SaveBoostReward(), 0.5),                     # Small reward for maintaining boost
        (BoostAcquisitions(), 3)                      # Reward for collecting boost pads
    )
    global g_combined_reward
    g_combined_reward = reward_fn


    obs_builder = AdvancedObsPadder()

    env = rlgym_sim.make(tick_skip=tick_skip,
                         team_size=team_size,
                         spawn_opponents=spawn_opponents,
                         terminal_conditions=terminal_conditions,
                         reward_fn=reward_fn,
                         obs_builder=obs_builder,
                         action_parser=action_parser
                         state_setter=ProbabilisticStateSetter(all_states, [1/len(all_states)]*len(all_states))
                         )

    import rocketsimvis_rlgym_sim_client as rsv
    type(env).render = lambda self: rsv.send_state_to_rocketsimvis(self._prev_state)
    
    return env

if __name__ == "__main__":
    from rlgym_ppo import Learner
    metrics_logger = ExampleLogger()

    # 32 processes
    n_proc = 32

    # educated guess - could be slightly higher or lower
    min_inference_size = max(1, int(round(n_proc * 0.9)))

    learner = Learner(build_rocketsim_env,
                      n_proc=n_proc,
                      min_inference_size=min_inference_size,
                      metrics_logger=metrics_logger,
                      ppo_batch_size=100_000,
                      ts_per_iteration=100_000,
                      exp_buffer_size=200_000,
                      ppo_minibatch_size=50_000,
                      ppo_ent_coef=0.01,
                      ppo_epochs=2,
                      standardize_returns=True,
                      standardize_obs=False,
                      save_every_ts=10_000_000,
                      timestep_limit=100_000_000_000,
                      log_to_wandb=True,
                      policy_layer_sizes=(2048, 1024, 512, 512),   # change it if you need
                      critic_layer_sizes=(2048, 1024, 512, 512), # same
                      device="dml",
                      render=True,
                      render_delay=0.055,
                      policy_lr=1e-4, 
                      critic_lr=1e-4,
                      wandb_run_name="Nexto Training 001",)
    
    build_rocketsim_env()  # Ensure the environment is built before learning

    learner.learn()
